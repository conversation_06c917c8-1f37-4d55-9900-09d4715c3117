import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { monthlyRentTrends } from '../../data/realEstateData';

const RentTrendsChart = () => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={monthlyRentTrends}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis />
        <Tooltip 
          formatter={(value) => [`$${value}`, '']}
          labelFormatter={(label) => `Month: ${label}`}
        />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="retail" 
          stroke="#8884d8" 
          strokeWidth={2}
          name="Retail"
        />
        <Line 
          type="monotone" 
          dataKey="office" 
          stroke="#82ca9d" 
          strokeWidth={2}
          name="Office"
        />
        <Line 
          type="monotone" 
          dataKey="restaurant" 
          stroke="#ffc658" 
          strokeWidth={2}
          name="Restaurant"
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default RentTrendsChart;