import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { zipCodeAnalytics } from '../../data/realEstateData';

const ZipCodeAnalyticsChart = () => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={zipCodeAnalytics}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="zipCode" />
        <YAxis yAxisId="left" orientation="left" />
        <YAxis yAxisId="right" orientation="right" />
        <Tooltip 
          formatter={(value, name) => {
            if (name === 'avgRent') return [`$${value}`, 'Avg Rent/sqft'];
            if (name === 'vacancyRate') return [`${value}%`, 'Vacancy Rate'];
            return [value, 'Properties'];
          }}
        />
        <Legend />
        <Bar 
          yAxisId="left"
          dataKey="avgRent" 
          fill="#8884d8" 
          name="Avg Rent/sqft ($)"
        />
        <Bar 
          yAxisId="right"
          dataKey="vacancyRate" 
          fill="#82ca9d" 
          name="Vacancy Rate (%)"
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default ZipCodeAnalyticsChart;