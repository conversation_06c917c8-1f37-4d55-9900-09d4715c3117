import React from 'react';
import PropertyMap from './PropertyMap';
import PropertyTypeChart from './charts/PropertyTypeChart';
import RentTrendsChart from './charts/RentTrendsChart';
import ZipCodeAnalyticsChart from './charts/ZipCodeAnalyticsChart';
import FootTrafficChart from './charts/FootTrafficChart';
import StatsCard from './StatsCard';
import { marketStats } from '../data/realEstateData';

const Dashboard = () => {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-semibold text-gray-800">Commercial Real Estate Discovery</h1>
      
      {/* Market Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {marketStats.map((stat, index) => (
          <StatsCard 
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            trend={stat.trend}
            icon={stat.icon}
          />
        ))}
      </div>
      
      {/* Property Map - Full Width */}
      <PropertyMap />
      
      {/* Analytics Charts - First Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Rent Trends by Property Type</h2>
          <RentTrendsChart />
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Property Distribution</h2>
          <PropertyTypeChart />
        </div>
      </div>
      
      {/* Analytics Charts - Second Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">ZIP Code Market Analysis</h2>
          <ZipCodeAnalyticsChart />
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Foot Traffic Patterns</h2>
          <FootTrafficChart />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;