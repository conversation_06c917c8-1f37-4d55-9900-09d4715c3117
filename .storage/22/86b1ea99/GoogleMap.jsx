import React, { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { propertyListings } from '../data/realEstateData';

const GoogleMap = ({ selectedProperty, onPropertySelect }) => {
  const mapRef = useRef(null);
  const [map, setMap] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [infoWindow, setInfoWindow] = useState(null);

  useEffect(() => {
    const loader = new Loader({
      apiKey: "AIzaSyDT_5w6Tl13cF3hQ3DINP60F5CVOSQ1pGs",
      version: "weekly",
      libraries: ["places"]
    });

    loader.load().then(() => {
      const mapInstance = new window.google.maps.Map(mapRef.current, {
        center: { lat: 40.7580, lng: -73.9855 }, // NYC center
        zoom: 13,
        styles: [
          {
            featureType: "poi.business",
            stylers: [{ visibility: "off" }]
          }
        ]
      });

      const infoWindowInstance = new window.google.maps.InfoWindow();
      
      setMap(mapInstance);
      setInfoWindow(infoWindowInstance);
    });
  }, []);

  useEffect(() => {
    if (!map || !window.google) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    // Create new markers for properties
    const newMarkers = propertyListings.map(property => {
      const marker = new window.google.maps.Marker({
        position: { lat: property.latitude, lng: property.longitude },
        map: map,
        title: property.address,
        icon: {
          url: getMarkerIcon(property.propertyType),
          scaledSize: new window.google.maps.Size(40, 40)
        }
      });

      marker.addListener('click', () => {
        const content = `
          <div style="max-width: 300px;">
            <h3 style="margin: 0 0 8px 0; font-weight: 600;">${property.address}</h3>
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span style="background: #dbeafe; color: #1e40af; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                ${property.propertyType}
              </span>
              <span style="color: #16a34a; font-weight: 600;">
                $${property.monthlyRent.toLocaleString()}/mo
              </span>
            </div>
            <p style="margin: 8px 0; color: #6b7280; font-size: 14px;">
              ${property.sqft.toLocaleString()} sqft • $${property.pricePerSqft}/sqft
            </p>
            <p style="margin: 8px 0; font-size: 14px;">${property.description}</p>
            <div style="margin-top: 12px; padding-top: 8px; border-top: 1px solid #e5e7eb;">
              <p style="margin: 0; font-size: 12px; color: #6b7280;">
                Agent: ${property.agent.name}<br>
                ${property.agent.phone}
              </p>
            </div>
            <button 
              onclick="window.selectProperty(${property.id})"
              style="
                margin-top: 8px; 
                background: #2563eb; 
                color: white; 
                padding: 6px 12px; 
                border: none; 
                border-radius: 4px; 
                cursor: pointer;
                font-size: 12px;
              "
            >
              View Details
            </button>
          </div>
        `;

        infoWindow.setContent(content);
        infoWindow.open(map, marker);
      });

      return marker;
    });

    setMarkers(newMarkers);

    // Global function to handle property selection from info window
    window.selectProperty = (propertyId) => {
      const property = propertyListings.find(p => p.id === propertyId);
      if (property && onPropertySelect) {
        onPropertySelect(property);
      }
      infoWindow.close();
    };

    return () => {
      window.selectProperty = undefined;
    };
  }, [map, infoWindow, onPropertySelect]);

  // Center map on selected property
  useEffect(() => {
    if (map && selectedProperty) {
      map.panTo({ 
        lat: selectedProperty.latitude, 
        lng: selectedProperty.longitude 
      });
      map.setZoom(15);
    }
  }, [map, selectedProperty]);

  const getMarkerIcon = (propertyType) => {
    const iconColors = {
      'Retail': '#3b82f6',      // blue
      'Office': '#10b981',      // green
      'Restaurant': '#f59e0b',  // orange
      'Warehouse': '#8b5cf6',   // purple
      'Mixed Use': '#ef4444'    // red
    };
    
    const color = iconColors[propertyType] || '#6b7280';
    
    return `data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 24 24' fill='${encodeURIComponent(color)}'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/%3E%3C/svg%3E`;
  };

  return (
    <div 
      ref={mapRef} 
      className="w-full h-64 rounded-lg border-2 border-gray-200"
      style={{ minHeight: '400px' }}
    />
  );
};

export default GoogleMap;