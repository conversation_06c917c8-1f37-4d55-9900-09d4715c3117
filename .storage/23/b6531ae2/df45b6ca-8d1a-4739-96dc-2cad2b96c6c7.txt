$ pnpm install @googlemaps/js-api-loader
Progress: resolved 1, reused 0, downloaded 0, added 0
Progress: resolved 23, reused 22, downloaded 0, added 0
Progress: resolved 349, reused 348, downloaded 1, added 0
 WARN  1 deprecated subdependencies found: rollup-plugin-terser@7.0.2
Progress: resolved 494, reused 452, downloaded 1, added 0
Packages: +1
+
Progress: resolved 494, reused 452, downloaded 1, added 1, done
 WARN  Issues with peer dependencies found
.
└─┬ @metagptx/vite-plugin-source-locator 0.0.6
  └─┬ rollup-plugin-terser 7.0.2
    └── ✕ unmet peer rollup@^2.0.0: found 4.48.1

dependencies:
+ @googlemaps/js-api-loader 1.16.10

╭ Warning ─────────────────────────────────────────────────────────────────────╮
│                                                                              │
│   Ignored build scripts: @metagptx/vite-plugin-source-locator, esbuild.      │
│   Run "pnpm approve-builds" to pick which dependencies should be allowed     │
│   to run scripts.                                                            │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯

Done in 4.2s using pnpm v10.10.0
