import React, { useState } from 'react';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import PropertyMap from './components/PropertyMap';

function App() {
  const [activeView, setActiveView] = useState('dashboard');

  const renderActiveView = () => {
    switch(activeView) {
      case 'dashboard':
        return <Dashboard />;
      case 'properties':
        return <PropertyMap />;
      case 'analytics':
        return <Dashboard />; // For now, using Dashboard for analytics
      case 'saved':
        return (
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Saved Properties</h2>
            <p className="text-gray-600">Your saved properties will appear here.</p>
          </div>
        );
      case 'reports':
        return (
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Market Reports</h2>
            <p className="text-gray-600">Market analysis reports coming soon.</p>
          </div>
        );
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Header />
      <div className="flex flex-1">
        <Sidebar activeView={activeView} onViewChange={setActiveView} />
        <main className="flex-1 p-6 overflow-auto">
          {renderActiveView()}
        </main>
      </div>
    </div>
  );
}

export default App;