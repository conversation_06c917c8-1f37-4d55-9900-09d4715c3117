{"name": "react-template", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@10.10.0", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint ./src --quiet", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^6.0.2", "@supabase/supabase-js": "^2.47.12", "@types/prop-types": "^15.7.14", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^2.15.1", "@metagptx/vite-plugin-source-locator": "latest"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.45", "tailwindcss": "^3.4.10", "vite": "^5.4.1"}}