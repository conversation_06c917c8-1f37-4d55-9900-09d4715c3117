{"tabs": {"00": {"tab_id": "00", "cwd": "/workspace/dashboard", "observer": {"block": "Terminal", "uuid": "df45b6ca-8d1a-4739-96dc-2cad2b96c6c7", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}}, "current_tab_id": "00", "forbidden_commands": {"run preview": "Use Deployer.deploy_to_public instead.", "serve ": "Use Deployer.deploy_to_public instead.", "python -m http.server": "Use python -u -m http.server port_number instead."}, "timeout": 300.0, "working_dir": "/workspace"}