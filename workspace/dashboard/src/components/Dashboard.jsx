import React from 'react';
import PropertyMap from './PropertyMap';
import PropertyTypeChart from './charts/PropertyTypeChart';
import RentTrendsChart from './charts/RentTrendsChart';
import ZipCodeAnalyticsChart from './charts/ZipCodeAnalyticsChart';
import FootTrafficChart from './charts/FootTrafficChart';
import StatsCard from './StatsCard';
import { marketStats } from '../data/realEstateData';

const Dashboard = () => {
  return (
    <div className="space-y-8 animate-slide-in">
      {/* Header Section */}
      <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
              Commercial Real Estate Discovery
            </h1>
            <p className="text-lg text-gray-600 font-medium">
              Real-time market insights and property analytics
            </p>
          </div>
          <div className="hidden lg:flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Last Updated</p>
              <p className="text-lg font-semibold text-gray-800">{new Date().toLocaleTimeString()}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Market Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {marketStats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            trend={stat.trend}
            icon={stat.icon}
          />
        ))}
      </div>
      
      {/* Property Map - Full Width */}
      <PropertyMap />
      
      {/* Analytics Charts - First Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Rent Trends by Property Type</h2>
          <RentTrendsChart />
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Property Distribution</h2>
          <PropertyTypeChart />
        </div>
      </div>
      
      {/* Analytics Charts - Second Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">ZIP Code Market Analysis</h2>
          <ZipCodeAnalyticsChart />
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Foot Traffic Patterns</h2>
          <FootTrafficChart />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;