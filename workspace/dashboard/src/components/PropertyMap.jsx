import React, { useState } from 'react';
import GoogleMap from './GoogleMap';
import { propertyListings } from '../data/realEstateData';

const PropertyMap = () => {
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [filters, setFilters] = useState({
    propertyType: 'all',
    maxRent: 20000,
    minSqft: 0
  });

  const filteredProperties = propertyListings.filter(property => {
    return (
      (filters.propertyType === 'all' || property.propertyType.toLowerCase() === filters.propertyType) &&
      property.monthlyRent <= filters.maxRent &&
      property.sqft >= filters.minSqft
    );
  });

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium text-gray-800">Property Map & Listings</h2>
        <div className="flex space-x-4">
          <select 
            className="border rounded px-3 py-1 text-sm"
            value={filters.propertyType}
            onChange={(e) => setFilters({...filters, propertyType: e.target.value})}
          >
            <option value="all">All Types</option>
            <option value="retail">Retail</option>
            <option value="office">Office</option>
            <option value="restaurant">Restaurant</option>
          </select>
          <input
            type="number"
            placeholder="Max Rent"
            className="border rounded px-3 py-1 text-sm w-24"
            value={filters.maxRent}
            onChange={(e) => setFilters({...filters, maxRent: parseInt(e.target.value) || 20000})}
          />
        </div>
      </div>
      
      {/* Google Maps Integration */}
      <GoogleMap 
        selectedProperty={selectedProperty}
        onPropertySelect={setSelectedProperty}
      />

      {/* Property Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredProperties.map((property) => (
          <div 
            key={property.id}
            className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => setSelectedProperty(property)}
          >
            <div className="flex justify-between items-start mb-2">
              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                {property.propertyType}
              </span>
              <span className="text-green-600 font-semibold">${property.monthlyRent.toLocaleString()}/mo</span>
            </div>
            
            <h3 className="font-medium text-gray-800 mb-1">{property.address}</h3>
            <p className="text-sm text-gray-600 mb-2">{property.sqft.toLocaleString()} sqft • ${property.pricePerSqft}/sqft</p>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Agent: {property.agent.name}</span>
              <button className="text-blue-600 hover:text-blue-800">Contact</button>
            </div>
          </div>
        ))}
      </div>

      {/* Property Detail Modal */}
      {selectedProperty && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold">{selectedProperty.address}</h3>
              <button 
                onClick={() => setSelectedProperty(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span>{selectedProperty.propertyType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Size:</span>
                <span>{selectedProperty.sqft.toLocaleString()} sqft</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Monthly Rent:</span>
                <span className="text-green-600 font-semibold">${selectedProperty.monthlyRent.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Price/sqft:</span>
                <span>${selectedProperty.pricePerSqft}</span>
              </div>
              
              <div className="pt-2">
                <p className="text-sm text-gray-600 mb-2">Amenities:</p>
                <div className="flex flex-wrap gap-1">
                  {selectedProperty.amenities.map((amenity, index) => (
                    <span key={index} className="bg-gray-100 text-xs px-2 py-1 rounded">
                      {amenity}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="pt-2">
                <p className="text-sm text-gray-700">{selectedProperty.description}</p>
              </div>
              
              <div className="pt-4 border-t">
                <p className="text-sm font-medium">Contact Agent:</p>
                <p className="text-sm">{selectedProperty.agent.name}</p>
                <p className="text-sm text-blue-600">{selectedProperty.agent.phone}</p>
                <p className="text-sm text-blue-600">{selectedProperty.agent.email}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PropertyMap;