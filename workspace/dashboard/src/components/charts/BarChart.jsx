import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON>xis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { barChartData } from '../../data/mockData';

const BarChartComponent = () => {
  return (
    <div className="h-72">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={barChartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="online" fill="#8884d8" />
          <Bar dataKey="inStore" fill="#82ca9d" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BarChartComponent;