import React from 'react';
import { AreaChart, Area, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { footTrafficData } from '../../data/realEstateData';

const FootTrafficChart = () => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={footTrafficData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="hour" />
        <YAxis />
        <Tooltip 
          formatter={(value) => [value, 'People/Hour']}
        />
        <Legend />
        <Area
          type="monotone"
          dataKey="weekday"
          stackId="1"
          stroke="#8884d8"
          fill="#8884d8"
          name="Weekday Traffic"
        />
        <Area
          type="monotone"
          dataKey="weekend"
          stackId="2"
          stroke="#82ca9d"
          fill="#82ca9d"
          name="Weekend Traffic"
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default FootTraffic<PERSON>hart;